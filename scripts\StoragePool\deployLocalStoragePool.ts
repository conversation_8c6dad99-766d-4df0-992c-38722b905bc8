import { ethers, upgrades } from "hardhat";
import { HardhatRuntimeEnvironment } from "hardhat/types";
import { parseUnits } from "ethers";

// Import hardhat globally for verification
declare const hre: HardhatRuntimeEnvironment;

async function main() {
  console.log("Deploying StoragePool locally on Hardhat network...");
  const [deployer, admin, user1, user2] = await ethers.getSigners();
  console.log("Deploying contracts with the account:", deployer.address);

  // Get the contract factories
  const StorageToken = await ethers.getContractFactory("StorageToken");
  const StakingPool = await ethers.getContractFactory("StakingPool");
  const StoragePool = await ethers.getContractFactory("StoragePool");

  // Configuration values
  const initialOwner = deployer.address;
  const initialAdmin = admin.address;
  // Large token supply for testing
  const tokenSupply = parseUnits("**********", 18); // 2 billion tokens
  const poolInitialAmount = parseUnits("********", 18); // 10M tokens for staking pool
  const userTestAmount = parseUnits("1000000", 18); // 1M tokens for users

  // Constants for governance roles
  const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));

  console.log("Using configuration:");
  console.log("- Initial Owner:", initialOwner);
  console.log("- Initial Admin:", initialAdmin);
  console.log("- Token Supply:", ethers.formatEther(tokenSupply));
  console.log("- Pool Initial Amount:", ethers.formatEther(poolInitialAmount));
  console.log("- User Test Amount:", ethers.formatEther(userTestAmount));

  try {
    // 1. Deploy StorageToken
    console.log("\nDeploying StorageToken as UUPS proxy...");
    const storageToken = await upgrades.deployProxy(
      StorageToken,
      [initialOwner, initialAdmin, tokenSupply],
      {
        initializer: "initialize",
        kind: "uups",
      }
    );
    await storageToken.waitForDeployment();
    const tokenAddress = await storageToken.getAddress();
    console.log("StorageToken deployed to:", tokenAddress);

    // Set up token governance
    console.log("\nSetting up token governance parameters...");
    // Increase time to bypass timelock
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]); // +1 day
    await ethers.provider.send("evm_mine", []);

    await storageToken.connect(deployer).setRoleQuorum(ADMIN_ROLE, 2);

    // Wait for timelock again
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]); // +1 day
    await ethers.provider.send("evm_mine", []);

    await storageToken
      .connect(deployer)
      .setRoleTransactionLimit(ADMIN_ROLE, tokenSupply);
    console.log("Token governance parameters set");

    // 2. Deploy StakingPool as UUPS proxy
    console.log("\nDeploying StakingPool as UUPS proxy...");
    const stakingPool = await upgrades.deployProxy(
      StakingPool,
      [tokenAddress, initialOwner, initialAdmin],
      {
        initializer: "initialize",
        kind: "uups",
      }
    );
    await stakingPool.waitForDeployment();
    const stakingPoolAddress = await stakingPool.getAddress();
    console.log("StakingPool deployed to:", stakingPoolAddress);

    // 3. Deploy StoragePool as UUPS proxy
    console.log("\nDeploying StoragePool as UUPS proxy...");
    const storagePool = await upgrades.deployProxy(
      StoragePool,
      [tokenAddress, stakingPoolAddress, initialOwner, initialAdmin],
      {
        initializer: "initialize",
        kind: "uups",
      }
    );
    await storagePool.waitForDeployment();
    const storagePoolAddress = await storagePool.getAddress();
    console.log("StoragePool deployed to:", storagePoolAddress);

    // 4. Set up governance parameters for the pools
    console.log("\nSetting up governance parameters for pools...");

    // Wait for timelock periods to expire for both pools
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]); // +1 day
    await ethers.provider.send("evm_mine", []);

    // Set quorum for both pools
    await stakingPool.connect(deployer).setRoleQuorum(ADMIN_ROLE, 2);
    await storagePool.connect(deployer).setRoleQuorum(ADMIN_ROLE, 2);

    // Wait for timelock periods again
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]); // +1 day
    await ethers.provider.send("evm_mine", []);

    // Set transaction limits
    await stakingPool
      .connect(deployer)
      .setRoleTransactionLimit(ADMIN_ROLE, tokenSupply);
    await storagePool
      .connect(deployer)
      .setRoleTransactionLimit(ADMIN_ROLE, tokenSupply);
    console.log("Governance parameters set up for both pools");

    // 5. Set up permissions for StoragePool
    console.log("\nSetting up permissions...");

    // Set StoragePool address as the staking engine on StakingPool
    console.log(
      "Setting StoragePool address as staking engine on StakingPool..."
    );
    await stakingPool.connect(deployer).setStakingEngine(storagePoolAddress);
    console.log("StakingPool configured with StoragePool address");

    // 6. Set up token governance with admin wallet
    console.log("\nSetting up token governance with admin wallet...");
    const storageTokenWithAdmin = storageToken.connect(admin);
    await storageTokenWithAdmin.setRoleQuorum(ADMIN_ROLE, 2);
    console.log("Token quorum set with admin");
    await storageTokenWithAdmin.setRoleTransactionLimit(
      ADMIN_ROLE,
      tokenSupply
    );
    console.log("Token transaction limit set with admin");

    // 7. Whitelist StakingPool in StorageToken via proposal mechanism
    console.log("\nWhitelisting StakingPool in StorageToken via proposal...");
    const storageTokenWithOwner = storageToken.connect(deployer);
    const ADD_WHITELIST_TYPE = 5;
    const ZERO_HASH = ethers.ZeroHash;
    const ZERO_ADDRESS = ethers.ZeroAddress;
    const whitelistProposalTx = await storageTokenWithAdmin.createProposal(
      ADD_WHITELIST_TYPE, 0, stakingPoolAddress, ZERO_HASH, 0, ZERO_ADDRESS
    );
    const whitelistReceipt = await whitelistProposalTx.wait();
    const proposalCreatedLog = whitelistReceipt!.logs.find(log => {
      try {
        const parsed = storageToken.interface.parseLog(log);
        return parsed?.name === "ProposalCreated";
      } catch {
        return false;
      }
    });
    const whitelistProposalId = proposalCreatedLog ?
      storageToken.interface.parseLog(proposalCreatedLog)?.args[0] :
      undefined;
    console.log("Whitelist proposalID:", whitelistProposalId);
    await storageTokenWithOwner.approveProposal(whitelistProposalId);
    console.log("Proposal approved by second admin");
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]);
    await ethers.provider.send("evm_mine", []);
    await storageTokenWithAdmin.executeProposal(whitelistProposalId);
    console.log("Whitelist proposal executed");
    await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]);
    await ethers.provider.send("evm_mine", []);

    // 8. Transfer tokens to staking pool for testing
    console.log("\nTransferring tokens to staking pool...");
    await storageTokenWithAdmin.transferFromContract(
      stakingPoolAddress,
      poolInitialAmount
    );
    console.log("Tokens transferred to staking pool");

    // 9. Transfer tokens to users for testing
    console.log("\nTransferring tokens to test users...");
    await storageTokenWithAdmin.transferFromContract(
      user1.address,
      userTestAmount
    );
    await storageTokenWithAdmin.transferFromContract(
      user2.address,
      userTestAmount
    );
    console.log("Tokens transferred to test users");

    // 10. Get balances for verification
    console.log("\nFetching balances...");
    const deployerBalance = await storageToken.balanceOf(deployer.address);
    const adminBalance = await storageToken.balanceOf(admin.address);
    const stakingPoolBalance = await storageToken.balanceOf(stakingPoolAddress);
    const user1Balance = await storageToken.balanceOf(user1.address);
    const user2Balance = await storageToken.balanceOf(user2.address);

    console.log("Token balances:");
    console.log(`- Deployer: ${ethers.formatEther(deployerBalance)}`);
    console.log(`- Admin: ${ethers.formatEther(adminBalance)}`);
    console.log(`- StakingPool: ${ethers.formatEther(stakingPoolBalance)}`);
    console.log(`- User1: ${ethers.formatEther(user1Balance)}`);
    console.log(`- User2: ${ethers.formatEther(user2Balance)}`);

    // 10. Summary
    console.log("\nDeployment completed successfully!");
    console.log("Summary:");
    console.log("- Storage Token:", tokenAddress);
    console.log("- Staking Pool:", stakingPoolAddress);
    console.log("- Storage Pool:", storagePoolAddress);
    console.log("\nTest accounts:");
    console.log("- Deployer (Owner):", deployer.address);
    console.log("- Admin:", admin.address);
    console.log("- User1:", user1.address);
    console.log("- User2:", user2.address);
    console.log("\nFeel free to interact with these contracts in your tests.");

    // 11. Test basic functionality
    console.log("\nTesting basic functionality...");

    // Test StoragePool initialization
    const tokenPoolAddress = await storagePool.tokenPool();
    console.log("StoragePool tokenPool address:", tokenPoolAddress);
    console.log("Expected StakingPool address:", stakingPoolAddress);
    console.log(
      "TokenPool correctly set:",
      tokenPoolAddress === stakingPoolAddress
    );

    // Test StakingPool initialization
    const stakingEngineAddress = await stakingPool.stakingEngine();
    console.log("StakingPool stakingEngine address:", stakingEngineAddress);
    console.log("Expected StoragePool address:", storagePoolAddress);
    console.log(
      "StakingEngine correctly set:",
      stakingEngineAddress === storagePoolAddress
    );
  } catch (error: any) {
    console.error("Deployment failed:", error.message);
    if (error.data) {
      console.error("Error data:", error.data);
    }
    if (error.stack) {
      console.error("Stack trace:", error.stack);
    }
    process.exit(1);
  }
}

// Execute the main function and handle any errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });

// Run with:
// npx hardhat node
// npx hardhat run scripts/StoragePool/deployLocalStoragePool.ts --network localhost
/*
Advance the time for testing:
npx hardhat console --network localhost
> await network.provider.send("evm_increaseTime", [86400]) // 1 day
> await network.provider.send("evm_mine")
 */
